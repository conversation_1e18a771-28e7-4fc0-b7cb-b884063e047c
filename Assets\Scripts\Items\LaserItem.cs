using UnityEngine;
using Fusion;
using SimpleFPS;
using System.Collections;
using DG.Tweening;

namespace SimpleFPS {
    public class LaserItem : PickupItem {

        [Header("LaserItem Settings")]
        public float tagDistance = 50f;
        public LayerMask hitLayer;

        [Header("Laser settings")]
        public float laserDistance = 60f;
        public float laserLifeTime = 0.01f;
        public Material laserMaterial;
        public float laserWidth = 0.02f;

        [Header("Cooldown settings")]
        public float cooldownDuration = 1.5f; // Cooldown in seconds

        // Local cooldown tracking (not networked)
        private bool isOnCooldown = false;
        private float cooldownStartTime = 0f;

        public override void ItemAbility(PlayerController player) {
            if (!Object.HasInputAuthority) return;

            // Check if laser is on cooldown
            if (isOnCooldown) {
                return;
            }

            // Start cooldown
            StartCooldown();

            // Calculate hit position for VFX
            Vector3 origin = player.laserBeamSource2.position;
            Vector3 direction = player.laserBeamSource2.forward;
            Vector3 hitPosition = origin + direction * laserDistance;

            // Perform raycast to get actual hit position
            if (Physics.Raycast(origin, direction, out RaycastHit hit, laserDistance, hitLayer == 0 ? ~0 : hitLayer)) {
                hitPosition = hit.point;
            }

            RPC_AttemptKill(player.Object.InputAuthority);

            SpawnLaser(player.laserBeamSource2, hitPosition);
        }

        void SpawnLaser(Transform source, Vector3 hitPosition) {
            // Activate the laser beam VFX
            source.gameObject.SetActive(true);

            // Get the LaserBeamController and set the hit target using reflection
            var components = source.GetComponents<Component>();
            foreach (var comp in components) {
                if (comp.GetType().Name == "LaserBeamController") {
                    var setHitPositionMethod = comp.GetType().GetMethod("SetHitPosition");
                    if (setHitPositionMethod != null) {
                        setHitPositionMethod.Invoke(comp, new object[] { hitPosition });
                    }
                    break;
                }
            }

            // Use DOTween to deactivate after laserLifeTime
            DOVirtual.DelayedCall(laserLifeTime, () => {
                if (source != null && source.gameObject != null) {
                    source.gameObject.SetActive(false);
                }
            });
        }


        // state‑authority: ищем Hitbox через LagCompensation
        [Rpc(RpcSources.InputAuthority, RpcTargets.StateAuthority)]
        void RPC_AttemptKill(PlayerRef shooterRef, RpcInfo _ = default) {

            if (!Object.HasStateAuthority || AltPryatkiGameMode.Instance == null) {
                return;
            }

            var shooterObj = Runner.GetPlayerObject(shooterRef);

            if (!shooterObj) {
                return;
            }
            var shooter = shooterObj.GetComponent<PlayerController>();

            Vector3 origin = shooter.laserBeamSource2.position;
            Vector3 direction = shooter.laserBeamSource2.forward;

            if (Runner.LagCompensation.Raycast(origin,
                                               direction,
                                               tagDistance,
                                               shooterRef,
                                               out LagCompensatedHit hit,
                                               hitLayer == 0 ? ~0 : hitLayer,
                                               HitOptions.IncludePhysX)) {

                var root = hit.Hitbox.Root;
                PlayerController victim = root ? root.GetComponent<PlayerController>() : null;

                var throwerObj = Runner.GetPlayerObject(Object.InputAuthority);
                PlayerController thrower = throwerObj ? throwerObj.GetComponent<PlayerController>() : null;

                if (victim && victim.Object.InputAuthority != shooterRef) {
                    // In AltPryatki mode, LaserItem kills the victim instead of tagging
                    AltPryatkiGameMode.Instance.OnPlayerKilled(shooterRef, victim.Object.InputAuthority);

                    // Actually kill the player using PlayerDeath component
                    PlayerDeath playerDeath = victim.GetComponent<PlayerDeath>();
                    if (playerDeath != null) {
                        playerDeath.DieFromExplosion(shooterRef);
                    }
                }

                if (thrower) {
                    thrower.RPC_ShowHitMarker();
                }

                if (victim) {
                    victim.RPC_ShowGotHit();
                }
            }
        }

        // Override pickup behavior to transfer leadership
        [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
        public new void RPC_RequestPickup(PlayerRef playerRef) {
            // Check if item can be picked up.
            if (!IsCanBeUse()) {
                return;
            }

            // Retrieve the PlayerController from the runner using the player's reference.
            PlayerController player = Runner.GetPlayerObject(playerRef).GetComponent<PlayerController>();

            // First, spawn the in-hand version of the pickup item.
            NetworkObject newItem = null;
            if (inHandTPO.IsValid) {
                Vector3 spawnPos = player.thirdPersonItemHolder.position;
                Quaternion spawnRot = player.thirdPersonItemHolder.rotation;
                newItem = Runner.Spawn(inHandTPO, spawnPos, spawnRot, playerRef);

                // Assign the new spawned item to the player's current item.
                player.CurrentItem = newItem;
            }

            // Notify AltPryatkiGameMode about the pickup to transfer leadership
            if (AltPryatkiGameMode.Instance != null) {
                AltPryatkiGameMode.Instance.OnLaserItemPickedUp(playerRef);
            }

            // Reset cooldown when item is picked up (only for the player who picked it up)
            if (newItem != null && newItem.TryGetComponent<LaserItem>(out var newLaserItem)) {
                newLaserItem.ResetCooldown();
            }

            // Reset cooldown when item is picked up (only for the player who picked it up)
            if (newItem != null && newItem.TryGetComponent<LaserItem>(out var inHandLaserItem)) {
                inHandLaserItem.ResetCooldown();
            }

            // Mark this item as unavailable and despawn it.
            IsAvailable = false;
            Runner.Despawn(Object);
        }

        private void StartCooldown() {
            isOnCooldown = true;
            cooldownStartTime = Time.time;
            Debug.Log($"LaserItem: Starting cooldown for {cooldownDuration} seconds");

            // Use DOTween to handle cooldown
            DOVirtual.DelayedCall(cooldownDuration, () => {
                isOnCooldown = false;
                Debug.Log("LaserItem: Cooldown finished, ready to fire");
            });
        }

        // Public methods for UI or other systems to check cooldown status
        public bool IsOnCooldown() {
            return isOnCooldown;
        }

        public float GetCooldownProgress() {
            if (!isOnCooldown) return 1f; // Fully ready

            float elapsed = Time.time - cooldownStartTime;
            return Mathf.Clamp01(elapsed / cooldownDuration);
        }

        public float GetRemainingCooldownTime() {
            if (!isOnCooldown) return 0f;

            float elapsed = Time.time - cooldownStartTime;
            return Mathf.Max(0f, cooldownDuration - elapsed);
        }

        public void ResetCooldown() {
            isOnCooldown = false;
            cooldownStartTime = 0f;
            Debug.Log("LaserItem: Cooldown reset");
        }
    }

    public class LaserBeamBehaviour : MonoBehaviour {
        LineRenderer lr;
        Transform src;
        float distance;
        float lifeTime;
        float timer;

        public void Initialize(LineRenderer line, Transform source, float dist, float duration) {
            lr = line;
            src = source;
            distance = dist;
            lifeTime = duration;
            timer = 0f;
        }

        void Update() {

            timer += Time.deltaTime;
            if (timer >= lifeTime) {
                Destroy(gameObject);
                return;
            }

            Vector3 origin = src.position;
            Vector3 end = origin + src.forward * distance;
            lr.SetPosition(0, origin);
            lr.SetPosition(1, end);
        }
    }
}
