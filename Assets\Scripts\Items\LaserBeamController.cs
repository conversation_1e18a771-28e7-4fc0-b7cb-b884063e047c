using UnityEngine;

namespace SimpleFPS {
    public class LaserBeamController : MonoBeh<PERSON>our {

        [Header("Target Settings")]
        [Tooltip("Target object that will be moved to hit position. This should be assigned to VFX Property Binder BeamEndPoint Target field.")]
        public Transform hitTarget;

        private Transform originalParent;
        private Vector3 originalLocalPosition;

        /// <summary>
        /// Sets the hit target position directly
        /// </summary>
        /// <param name="position">The world position where the laser hits</param>
        public void SetHitPosition(Vector3 position) {
            if (hitTarget != null) {
                // Save original parent and position before moving
                if (originalParent == null) {
                    originalParent = hitTarget.parent;
                    originalLocalPosition = hitTarget.localPosition;
                }

                // Move target out of player hierarchy to scene root to avoid coordinate issues
                hitTarget.SetParent(null);

                // Set target position directly in world coordinates
                hitTarget.position = position;
            }
        }

        /// <summary>
        /// Resets the target back to its original parent and position
        /// </summary>
        public void ResetTarget() {
            if (hitTarget != null && originalParent != null) {
                hitTarget.SetParent(originalParent);
                hitTarget.localPosition = originalLocalPosition;
                originalParent = null;
            }
        }

        /// <summary>
        /// Alternative method to set BeamEndPoint position directly
        /// </summary>
        /// <param name="endPoint">The end point position for the beam</param>
        public void SetBeamEndPoint(Vector3 endPoint) {
            SetHitPosition(endPoint);
        }
    }
}
