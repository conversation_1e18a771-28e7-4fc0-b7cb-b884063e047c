using UnityEngine;

namespace SimpleFPS {
    public class LaserBeamController : MonoBehaviour {

        [Header("VFX Settings")]
        public Component vfxPropertyBinder; // Using Component instead of VFXPropertyBinder for compatibility

        [Header("Target Settings")]
        public Transform hitTarget;

        private void Awake() {
            // Try to find VFX Property Binder component if not assigned
            if (vfxPropertyBinder == null) {
                // Look for VFXPropertyBinder component by name
                var components = GetComponents<Component>();
                foreach (var comp in components) {
                    if (comp.GetType().Name == "VFXPropertyBinder") {
                        vfxPropertyBinder = comp;
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// Sets the target transform for the VFX Property Binder using reflection
        /// </summary>
        /// <param name="target">The transform to target</param>
        public void SetHitTarget(Transform target) {
            hitTarget = target;

            if (vfxPropertyBinder != null) {
                // Use reflection to access VFX Property Binder bindings
                var binderType = vfxPropertyBinder.GetType();

                // Try to get the bindings list
                var bindingsField = binderType.GetField("m_Bindings", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (bindingsField != null) {
                    var bindings = bindingsField.GetValue(vfxPropertyBinder);
                    if (bindings != null) {
                        // Try to iterate through bindings to find BeamEndPoint
                        var bindingsType = bindings.GetType();
                        if (bindingsType.IsGenericType && bindingsType.GetGenericTypeDefinition() == typeof(System.Collections.Generic.List<>)) {
                            var bindingsList = (System.Collections.IList)bindings;
                            foreach (var binding in bindingsList) {
                                if (binding != null) {
                                    // Check if this binding has BeamEndPoint property
                                    var propertyField = binding.GetType().GetField("m_Property", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                                    if (propertyField != null) {
                                        var property = propertyField.GetValue(binding);
                                        if (property != null) {
                                            var nameField = property.GetType().GetField("m_Name", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                                            if (nameField != null) {
                                                var propertyName = nameField.GetValue(property) as string;
                                                if (propertyName == "BeamEndPoint") {
                                                    // Found BeamEndPoint binding, set its Target
                                                    var targetField = binding.GetType().GetField("Target", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                                                    if (targetField != null) {
                                                        targetField.SetValue(binding, target);
                                                        Debug.Log($"Successfully set BeamEndPoint Target to {target.name}");
                                                        return;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                Debug.LogWarning("Could not find BeamEndPoint binding in VFX Property Binder");
            }
        }

        /// <summary>
        /// Sets the target position directly for the VFX Property Binder BeamEndPoint property
        /// </summary>
        /// <param name="position">The world position to target</param>
        public void SetHitPosition(Vector3 position) {
            // Create a temporary transform at the hit position and use SetHitTarget
            GameObject tempTarget = new GameObject("LaserHitTarget");
            tempTarget.transform.position = position;
            hitTarget = tempTarget.transform;

            // Use the improved SetHitTarget method to set the BeamEndPoint target
            SetHitTarget(hitTarget);

            // Destroy the temporary object after a short delay
            Destroy(tempTarget, 1f);
        }

        /// <summary>
        /// Alternative method to set BeamEndPoint property directly
        /// </summary>
        /// <param name="endPoint">The end point position for the beam</param>
        public void SetBeamEndPoint(Vector3 endPoint) {
            SetHitPosition(endPoint);
        }
    }
}
