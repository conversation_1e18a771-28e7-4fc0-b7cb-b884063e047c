using UnityEngine;

namespace SimpleFPS
{
    public class LaserBeamController : MonoBehaviour
    {

        [Header("VFX Settings")]
        public Component vfxPropertyBinder; // Using Component instead of VFXPropertyBinder for compatibility

        [Header("Target Settings")]
        public Transform hitTarget;

        private void Awake()
        {
            // Try to find VFX Property Binder component if not assigned
            if (vfxPropertyBinder == null)
            {
                // Look for VFXPropertyBinder component by name
                var components = GetComponents<Component>();
                foreach (var comp in components)
                {
                    if (comp.GetType().Name == "VFXPropertyBinder")
                    {
                        vfxPropertyBinder = comp;
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// Sets the target transform for the VFX Property Binder using reflection
        /// </summary>
        /// <param name="target">The transform to target</param>
        public void SetHitTarget(Transform target)
        {
            hitTarget = target;

            if (vfxPropertyBinder != null)
            {
                // Use reflection to access VFX Property Binder methods
                var binderType = vfxPropertyBinder.GetType();
                var getPropertyBinderMethod = binderType.GetMethod("GetPropertyBinder");

                if (getPropertyBinderMethod != null)
                {
                    // Try to find position binder
                    try
                    {
                        var positionBinderType = System.Type.GetType("UnityEngine.VFX.Utility.VFXPositionBinder, Unity.VisualEffectGraph.Runtime");
                        if (positionBinderType != null)
                        {
                            var positionBinder = getPropertyBinderMethod.MakeGenericMethod(positionBinderType).Invoke(vfxPropertyBinder, null);
                            if (positionBinder != null)
                            {
                                var targetProperty = positionBinderType.GetProperty("Target");
                                if (targetProperty != null)
                                {
                                    targetProperty.SetValue(positionBinder, target);
                                }
                            }
                        }
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogWarning($"Could not set VFX Position Binder target: {e.Message}");
                    }

                    // Try to find transform binder
                    try
                    {
                        var transformBinderType = System.Type.GetType("UnityEngine.VFX.Utility.VFXTransformBinder, Unity.VisualEffectGraph.Runtime");
                        if (transformBinderType != null)
                        {
                            var transformBinder = getPropertyBinderMethod.MakeGenericMethod(transformBinderType).Invoke(vfxPropertyBinder, null);
                            if (transformBinder != null)
                            {
                                var targetProperty = transformBinderType.GetProperty("Target");
                                if (targetProperty != null)
                                {
                                    targetProperty.SetValue(transformBinder, target);
                                }
                            }
                        }
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogWarning($"Could not set VFX Transform Binder target: {e.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// Sets the target position directly for the VFX Property Binder
        /// </summary>
        /// <param name="position">The world position to target</param>
        public void SetHitPosition(Vector3 position)
        {
            // Create a temporary transform at the hit position
            GameObject tempTarget = new GameObject("LaserHitTarget");
            tempTarget.transform.position = position;
            hitTarget = tempTarget.transform;

            SetHitTarget(hitTarget);

            // Destroy the temporary object after a short delay
            Destroy(tempTarget, 1f);
        }
    }
}
