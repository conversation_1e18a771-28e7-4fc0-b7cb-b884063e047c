using UnityEngine;

namespace SimpleFPS {
    public class LaserBeamController : MonoBeh<PERSON><PERSON> {

        [Header("Target Settings")]
        [Tooltip("Target object that will be moved to hit position. This should be assigned to VFX Property Binder BeamEndPoint Target field.")]
        public Transform hitTarget;

        /// <summary>
        /// Sets the hit target position directly
        /// </summary>
        /// <param name="position">The world position where the laser hits</param>
        public void SetHitPosition(Vector3 position) {
            if (hitTarget != null) {
                // Convert world position to local position relative to this transform
                // since hitTarget is a child of this object (laserBeamSource2)
                Vector3 localPosition = transform.InverseTransformPoint(position);
                hitTarget.localPosition = localPosition;
            }
        }

        /// <summary>
        /// Alternative method to set BeamEndPoint position directly
        /// </summary>
        /// <param name="endPoint">The end point position for the beam</param>
        public void SetBeamEndPoint(Vector3 endPoint) {
            SetHitPosition(endPoint);
        }
    }
}
