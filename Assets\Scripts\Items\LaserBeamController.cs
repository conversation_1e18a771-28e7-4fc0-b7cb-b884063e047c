using UnityEngine;

namespace SimpleFPS {
    public class LaserBeamController : MonoBehaviour {

        [Header("VFX Settings")]
        public Component vfxPropertyBinder; // Using Component instead of VFXPropertyBinder for compatibility

        [Header("Target Settings")]
        public Transform hitTarget;

        private void Awake() {
            // Try to find VFX Property Binder component if not assigned
            if (vfxPropertyBinder == null) {
                // Look for VFXPropertyBinder component by name
                var components = GetComponents<Component>();
                foreach (var comp in components) {
                    if (comp.GetType().Name == "VFXPropertyBinder") {
                        vfxPropertyBinder = comp;
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// Sets the target transform for the VFX Property Binder using reflection
        /// </summary>
        /// <param name="target">The transform to target</param>
        public void SetHitTarget(Transform target) {
            hitTarget = target;

            if (vfxPropertyBinder != null) {
                // Use reflection to access VFX Property Binder methods
                var binderType = vfxPropertyBinder.GetType();
                var getPropertyBinderMethod = binderType.GetMethod("GetPropertyBinder");

                if (getPropertyBinderMethod != null) {
                    // Try to find position binder
                    try {
                        var positionBinderType = System.Type.GetType("UnityEngine.VFX.Utility.VFXPositionBinder, Unity.VisualEffectGraph.Runtime");
                        if (positionBinderType != null) {
                            var positionBinder = getPropertyBinderMethod.MakeGenericMethod(positionBinderType).Invoke(vfxPropertyBinder, null);
                            if (positionBinder != null) {
                                var targetProperty = positionBinderType.GetProperty("Target");
                                if (targetProperty != null) {
                                    targetProperty.SetValue(positionBinder, target);
                                }
                            }
                        }
                    }
                    catch (System.Exception e) {
                        Debug.LogWarning($"Could not set VFX Position Binder target: {e.Message}");
                    }

                    // Try to find transform binder
                    try {
                        var transformBinderType = System.Type.GetType("UnityEngine.VFX.Utility.VFXTransformBinder, Unity.VisualEffectGraph.Runtime");
                        if (transformBinderType != null) {
                            var transformBinder = getPropertyBinderMethod.MakeGenericMethod(transformBinderType).Invoke(vfxPropertyBinder, null);
                            if (transformBinder != null) {
                                var targetProperty = transformBinderType.GetProperty("Target");
                                if (targetProperty != null) {
                                    targetProperty.SetValue(transformBinder, target);
                                }
                            }
                        }
                    }
                    catch (System.Exception e) {
                        Debug.LogWarning($"Could not set VFX Transform Binder target: {e.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// Sets the target position directly for the VFX Property Binder BeamEndPoint property
        /// </summary>
        /// <param name="position">The world position to target</param>
        public void SetHitPosition(Vector3 position) {
            if (vfxPropertyBinder != null) {
                // Try to set BeamEndPoint property through VFX Property Binder
                try {
                    var binderType = vfxPropertyBinder.GetType();

                    // Try to find and set BeamEndPoint property directly on the binder
                    var beamEndPointProperty = binderType.GetProperty("BeamEndPoint");
                    if (beamEndPointProperty != null && beamEndPointProperty.PropertyType == typeof(Vector3)) {
                        beamEndPointProperty.SetValue(vfxPropertyBinder, position);
                    }

                    // Try to access the VFX component through the property binder
                    var vfxProperty = binderType.GetProperty("visualEffect") ?? binderType.GetProperty("VisualEffect");
                    if (vfxProperty != null) {
                        var visualEffect = vfxProperty.GetValue(vfxPropertyBinder);
                        if (visualEffect != null) {
                            var setVector3Method = visualEffect.GetType().GetMethod("SetVector3", new System.Type[] { typeof(string), typeof(Vector3) });
                            if (setVector3Method != null) {
                                setVector3Method.Invoke(visualEffect, new object[] { "BeamEndPoint", position });
                            }
                        }
                    }
                }
                catch (System.Exception e) {
                    Debug.LogWarning($"Could not set VFX BeamEndPoint through Property Binder: {e.Message}");
                }

                // Try to set BeamEndPoint property directly on VFX component
                try {
                    // Look for VFX component on the same GameObject
                    Component visualEffect = null;

                    var components = GetComponents<Component>();
                    foreach (var comp in components) {
                        if (comp.GetType().Name == "VisualEffect") {
                            visualEffect = comp;
                            break;
                        }
                    }

                    if (visualEffect != null) {
                        // Try to set Vector3 property directly
                        var setVector3Method = visualEffect.GetType().GetMethod("SetVector3", new System.Type[] { typeof(string), typeof(Vector3) });
                        if (setVector3Method != null) {
                            setVector3Method.Invoke(visualEffect, new object[] { "BeamEndPoint", position });

                            // Also try common variations
                            setVector3Method.Invoke(visualEffect, new object[] { "EndPoint", position });
                            setVector3Method.Invoke(visualEffect, new object[] { "Target", position });
                            setVector3Method.Invoke(visualEffect, new object[] { "HitPosition", position });
                        }
                    }
                }
                catch (System.Exception e) {
                    Debug.LogWarning($"Could not set VFX BeamEndPoint property directly: {e.Message}");
                }
            }

            // Fallback: Create a temporary transform at the hit position for property binders
            GameObject tempTarget = new GameObject("LaserHitTarget");
            tempTarget.transform.position = position;
            hitTarget = tempTarget.transform;

            SetHitTarget(hitTarget);

            // Destroy the temporary object after a short delay
            Destroy(tempTarget, 1f);
        }

        /// <summary>
        /// Alternative method to set BeamEndPoint property directly
        /// </summary>
        /// <param name="endPoint">The end point position for the beam</param>
        public void SetBeamEndPoint(Vector3 endPoint) {
            SetHitPosition(endPoint);
        }
    }
}
